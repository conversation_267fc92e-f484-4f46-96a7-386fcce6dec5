{"name": "justsimplechat", "version": "0.1.0", "private": true, "_deploymentTest": "2025-06-29T11:15:49Z - Testing autorouter deployment flow", "scripts": {"dev": "next dev --turbopack -p 3004", "dev:3008": "next dev -p 3008", "dev:start": "./start-dev.sh", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "test": "echo 'Tests will be added soon' && exit 0", "start:prod": "./start-prod.sh", "start:3005": "next start -p 3005 -H 0.0.0.0", "start:3006": "next start -p 3006 -H 0.0.0.0", "lint": "next lint", "typecheck": "tsc --noEmit", "check": "npm run typecheck && npm run lint", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:push": "prisma db push", "db:seed": "prisma db seed", "db:studio": "prisma studio", "sync-models": "tsx scripts/sync-models.ts", "db:reset": "prisma migrate reset", "test:email": "tsx scripts/test-email.ts", "ai-sdk:test": "tsx scripts/test-ai-sdk-providers.ts", "add-models": "node add-models.js", "add-models:dry": "node add-models.js", "deduplicate:all": "tsx src/scripts/deduplicate-all-conversations.ts", "deduplicate:check": "tsx src/scripts/check-duplicates.ts", "generate-stats": "node scripts/generate-model-stats.js", "ws:dev": "tsx watch src/lib/websocket-server.ts", "ws:start": "tsx src/lib/websocket-server.ts", "router-test": "tsx src/lib/ai/router-tester.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/cohere": "^1.2.10", "@ai-sdk/deepseek": "^0.2.15", "@ai-sdk/google": "^1.2.22", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/perplexity": "^1.1.9", "@ai-sdk/provider": "^1.1.3", "@ai-sdk/provider-utils": "^2.2.8", "@ai-sdk/togetherai": "^0.2.15", "@ai-sdk/xai": "^1.2.17", "@anthropic-ai/sdk": "^0.54.0", "@auth/core": "^0.39.1", "@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-bedrock-runtime": "^3.830.0", "@chakra-ui/react": "^3.21.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@google-cloud/aiplatform": "^4.2.0", "@google-cloud/vertexai": "^1.10.0", "@google/generative-ai": "^0.24.1", "@mistralai/mistralai": "^1.7.2", "@openrouter/ai-sdk-provider": "^0.7.2", "@prisma/client": "^6.9.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@sendgrid/mail": "^8.1.5", "@tanstack/react-query": "^5.80.7", "@tanstack/react-table": "^8.21.3", "@types/canvas-confetti": "^1.9.0", "@types/node": "20.19.1", "@types/pdf-parse": "^1.1.5", "@vercel/analytics": "^1.5.0", "ai": "^4.3.16", "axios": "^1.10.0", "bull": "^4.16.5", "canvas-confetti": "^1.9.3", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cohere-ai": "^7.17.1", "critters": "^0.0.23", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "docx-parser": "^0.2.1", "dotenv": "^16.5.0", "eventsource-parser": "^3.0.2", "framer-motion": "^12.18.1", "groq-sdk": "^0.25.0", "image-mcp": "^0.0.13", "immer": "10.0.3", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.514.0", "mammoth": "^1.9.1", "mysql2": "^3.14.1", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "next-seo": "^6.8.0", "next-sitemap": "^4.2.3", "node-fetch": "^2.7.0", "ollama": "^0.5.16", "openai": "^5.3.0", "pdf2json": "^3.1.6", "pdf2pic": "^3.2.0", "pdfjs-dist": "^5.3.93", "pg": "^8.16.2", "pino": "8.16.2", "pino-pretty": "10.3.0", "prisma": "^6.9.0", "prismjs": "^1.30.0", "qwen-ai-provider": "^0.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "react-markdown": "^10.1.0", "recharts": "^2.15.4", "remark-gfm": "^4.0.1", "schema-dts": "^1.1.5", "sonner": "^2.0.5", "stripe": "^18.2.1", "swr": "^2.3.3", "tailwind-merge": "^3.3.1", "tiktoken": "^1.0.21", "typescript": "^5.8.3", "ua-parser-js": "^2.0.4", "usehooks-ts": "^3.1.1", "ws": "^8.18.3", "xlsx": "^0.18.5", "zod": "^3.25.63", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.19", "@types/prismjs": "^1.26.5", "@types/react": "19.1.8", "@types/react-dom": "^19.1.6", "@types/ua-parser-js": "^0.7.39", "@types/ws": "^8.18.1", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "glob": "^11.0.3", "tailwindcss": "^4", "tsx": "^4.20.3"}}