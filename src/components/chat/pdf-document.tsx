/**
 * PDF Document Component for Chat Export
 * 
 * @description
 * This component creates a PDF-formatted document from chat conversation data
 * using @react-pdf/renderer. It handles:
 * - Message formatting with proper roles (user/assistant)
 * - Code blocks with syntax highlighting
 * - Attachments and metadata
 * - Conversation metadata (title, date, model info)
 * - Professional styling and layout
 */

import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Font,
  Image
} from '@react-pdf/renderer';
import { Message, MessageRole } from '@/types';

// Register fonts for better typography
Font.register({
  family: 'Inter',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyeMZhrib2Bg-4.woff2',
      fontWeight: 400,
    },
    {
      src: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuI6fAZhrib2Bg-4.woff2',
      fontWeight: 600,
    },
  ],
});

Font.register({
  family: 'JetBrains Mono',
  src: 'https://fonts.gstatic.com/s/jetbrainsmono/v13/tDbY2o-flEEny0FZhsfKu5WU4zr3E_BX0PnT8RD8yKxjPVmUsaaDhw.woff2',
});

// PDF Styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 40,
    fontFamily: 'Inter',
    fontSize: 11,
    lineHeight: 1.4,
  },
  header: {
    marginBottom: 30,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 600,
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
  },
  messageContainer: {
    marginBottom: 20,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
  },
  userMessage: {
    backgroundColor: '#f3f4f6',
    borderColor: '#d1d5db',
    marginLeft: 40,
  },
  assistantMessage: {
    backgroundColor: '#ecfdf5',
    borderColor: '#d1fae5',
    marginRight: 40,
  },
  systemMessage: {
    backgroundColor: '#fef3c7',
    borderColor: '#fde68a',
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  roleLabel: {
    fontSize: 10,
    fontWeight: 600,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  userRole: {
    color: '#374151',
  },
  assistantRole: {
    color: '#059669',
  },
  systemRole: {
    color: '#d97706',
  },
  timestamp: {
    fontSize: 9,
    color: '#9ca3af',
  },
  messageContent: {
    fontSize: 11,
    lineHeight: 1.5,
    color: '#374151',
  },
  codeBlock: {
    backgroundColor: '#1f2937',
    color: '#f9fafb',
    padding: 12,
    borderRadius: 6,
    fontFamily: 'JetBrains Mono',
    fontSize: 9,
    marginVertical: 8,
    lineHeight: 1.4,
  },
  codeInline: {
    backgroundColor: '#f3f4f6',
    color: '#374151',
    fontFamily: 'JetBrains Mono',
    fontSize: 10,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 3,
  },
  modelInfo: {
    fontSize: 9,
    color: '#6b7280',
    fontStyle: 'italic',
    marginTop: 4,
  },
  attachmentInfo: {
    fontSize: 9,
    color: '#7c3aed',
    marginTop: 4,
    fontStyle: 'italic',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 40,
    right: 40,
    textAlign: 'center',
    fontSize: 9,
    color: '#9ca3af',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 10,
  },
  pageNumber: {
    position: 'absolute',
    bottom: 30,
    right: 40,
    fontSize: 9,
    color: '#9ca3af',
  },
});

interface PDFDocumentProps {
  messages: Message[];
  conversationTitle: string;
  conversationId: string;
  exportDate?: Date;
  totalMessages?: number;
  totalTokens?: number;
}

// Helper function to format message content
const formatMessageContent = (content: string): React.ReactNode[] => {
  const parts: React.ReactNode[] = [];
  
  // Simple markdown-like parsing for code blocks and inline code
  const codeBlockRegex = /```[\s\S]*?```/g;
  const inlineCodeRegex = /`([^`]+)`/g;
  
  let lastIndex = 0;
  let match;
  
  // Handle code blocks
  while ((match = codeBlockRegex.exec(content)) !== null) {
    // Add text before code block
    if (match.index > lastIndex) {
      const textBefore = content.slice(lastIndex, match.index);
      if (textBefore.trim()) {
        parts.push(
          <Text key={`text-${lastIndex}`} style={styles.messageContent}>
            {textBefore}
          </Text>
        );
      }
    }
    
    // Add code block
    const codeContent = match[0].replace(/```[\w]*\n?/, '').replace(/```$/, '');
    parts.push(
      <View key={`code-${match.index}`} style={styles.codeBlock}>
        <Text>{codeContent}</Text>
      </View>
    );
    
    lastIndex = match.index + match[0].length;
  }
  
  // Add remaining text
  if (lastIndex < content.length) {
    const remainingText = content.slice(lastIndex);
    
    // Handle inline code in remaining text
    const textParts: React.ReactNode[] = [];
    let textLastIndex = 0;
    
    while ((match = inlineCodeRegex.exec(remainingText)) !== null) {
      if (match.index > textLastIndex) {
        textParts.push(remainingText.slice(textLastIndex, match.index));
      }
      textParts.push(
        <Text key={`inline-${match.index}`} style={styles.codeInline}>
          {match[1]}
        </Text>
      );
      textLastIndex = match.index + match[0].length;
    }
    
    if (textLastIndex < remainingText.length) {
      textParts.push(remainingText.slice(textLastIndex));
    }
    
    if (textParts.length > 0) {
      parts.push(
        <Text key={`text-${lastIndex}`} style={styles.messageContent}>
          {textParts}
        </Text>
      );
    }
  }
  
  return parts;
};

// Helper function to get role styling
const getRoleStyle = (role: MessageRole) => {
  switch (role) {
    case MessageRole.USER:
      return { container: styles.userMessage, label: styles.userRole };
    case MessageRole.ASSISTANT:
      return { container: styles.assistantMessage, label: styles.assistantRole };
    case MessageRole.SYSTEM:
      return { container: styles.systemMessage, label: styles.systemRole };
    default:
      return { container: styles.assistantMessage, label: styles.assistantRole };
  }
};

export const ChatPDFDocument: React.FC<PDFDocumentProps> = ({
  messages,
  conversationTitle,
  conversationId,
  exportDate = new Date(),
  totalMessages,
  totalTokens,
}) => {
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>{conversationTitle}</Text>
          <Text style={styles.subtitle}>
            Exported on {exportDate.toLocaleDateString()} at {exportDate.toLocaleTimeString()}
          </Text>
          <Text style={styles.subtitle}>
            Conversation ID: {conversationId}
          </Text>
          {totalMessages && (
            <Text style={styles.subtitle}>
              {totalMessages} messages
              {totalTokens && ` • ${totalTokens.toLocaleString()} tokens`}
            </Text>
          )}
        </View>

        {/* Messages */}
        {messages.map((message, index) => {
          const roleStyle = getRoleStyle(message.role);
          
          return (
            <View key={message.id} style={[styles.messageContainer, roleStyle.container]}>
              <View style={styles.messageHeader}>
                <Text style={[styles.roleLabel, roleStyle.label]}>
                  {message.role === MessageRole.USER ? 'You' : 
                   message.role === MessageRole.ASSISTANT ? 'Assistant' : 
                   message.role}
                </Text>
                <Text style={styles.timestamp}>
                  {new Date(message.createdAt).toLocaleString()}
                </Text>
              </View>
              
              {/* Message Content */}
              <View>
                {formatMessageContent(message.content)}
              </View>
              
              {/* Model Info */}
              {message.model && (
                <Text style={styles.modelInfo}>
                  Model: {message.model}
                  {message.tokens && ` • ${message.tokens.total} tokens`}
                </Text>
              )}
              
              {/* Attachments */}
              {message.attachments && message.attachments.length > 0 && (
                <Text style={styles.attachmentInfo}>
                  📎 {message.attachments.length} attachment{message.attachments.length > 1 ? 's' : ''}
                </Text>
              )}
            </View>
          );
        })}

        {/* Footer */}
        <Text style={styles.footer}>
          Generated by JustSimpleChat • {new Date().getFullYear()}
        </Text>
        
        <Text style={styles.pageNumber} render={({ pageNumber, totalPages }) => 
          `${pageNumber} / ${totalPages}`
        } fixed />
      </Page>
    </Document>
  );
};
