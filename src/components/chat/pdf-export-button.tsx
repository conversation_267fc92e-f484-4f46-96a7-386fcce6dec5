/**
 * PDF Export Button Component
 * 
 * @description
 * A button component that handles PDF export of chat conversations.
 * Includes loading states, error handling, and export options.
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Download, 
  FileText, 
  Loader2, 
  AlertCircle,
  CheckCircle,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Message } from '@/types';
import { PDFExportService, PDFExportOptions, ConversationData } from '@/lib/services/pdf-export.service';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { motion, AnimatePresence } from 'framer-motion';

interface PDFExportButtonProps {
  messages: Message[];
  conversationId: string;
  conversationTitle: string;
  className?: string;
  variant?: 'default' | 'ghost' | 'outline' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showDropdown?: boolean;
  disabled?: boolean;
}

type ExportState = 'idle' | 'loading' | 'success' | 'error';

export const PDFExportButton: React.FC<PDFExportButtonProps> = ({
  messages,
  conversationId,
  conversationTitle,
  className,
  variant = 'ghost',
  size = 'sm',
  showDropdown = true,
  disabled = false,
}) => {
  const [exportState, setExportState] = useState<ExportState>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Prepare conversation data
  const conversationData: ConversationData = {
    id: conversationId,
    title: conversationTitle,
    messages: messages,
    createdAt: messages[0]?.createdAt ? new Date(messages[0].createdAt) : new Date(),
    updatedAt: messages[messages.length - 1]?.createdAt ? new Date(messages[messages.length - 1].createdAt) : new Date(),
  };

  // Get export statistics
  const exportStats = PDFExportService.getExportStats(conversationData);

  const handleExport = async (options: PDFExportOptions = {}) => {
    if (disabled || exportState === 'loading') return;

    setExportState('loading');
    setErrorMessage('');

    try {
      // Validate conversation data
      const validation = PDFExportService.validateConversationData(conversationData);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Export to PDF
      await PDFExportService.exportConversationToPDF(conversationData, options);
      
      setExportState('success');
      
      // Reset to idle after success animation
      setTimeout(() => {
        setExportState('idle');
      }, 2000);

    } catch (error) {
      console.error('PDF export error:', error);
      setExportState('error');
      setErrorMessage(error instanceof Error ? error.message : 'Export failed');
      
      // Reset to idle after error display
      setTimeout(() => {
        setExportState('idle');
        setErrorMessage('');
      }, 3000);
    }
  };

  const getButtonContent = () => {
    switch (exportState) {
      case 'loading':
        return (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="ml-2">Generating...</span>
          </>
        );
      case 'success':
        return (
          <>
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span className="ml-2">Downloaded!</span>
          </>
        );
      case 'error':
        return (
          <>
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="ml-2">Failed</span>
          </>
        );
      default:
        return (
          <>
            <Download className="w-4 h-4" />
            <span className="ml-2">Export PDF</span>
          </>
        );
    }
  };

  const isDisabled = disabled || exportState === 'loading' || messages.length === 0;

  if (!showDropdown) {
    return (
      <Button
        onClick={() => handleExport()}
        disabled={isDisabled}
        variant={variant}
        size={size}
        className={cn(
          "transition-all duration-200",
          exportState === 'success' && "bg-green-500/10 border-green-500/20",
          exportState === 'error' && "bg-red-500/10 border-red-500/20",
          className
        )}
        title={errorMessage || `Export ${exportStats.messageCount} messages to PDF`}
      >
        {getButtonContent()}
      </Button>
    );
  }

  return (
    <div className="relative">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            disabled={isDisabled}
            variant={variant}
            size={size}
            className={cn(
              "transition-all duration-200",
              exportState === 'success' && "bg-green-500/10 border-green-500/20",
              exportState === 'error' && "bg-red-500/10 border-red-500/20",
              className
            )}
          >
            {getButtonContent()}
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Export Options
          </DropdownMenuLabel>
          
          <div className="px-2 py-1 text-xs text-gray-500">
            {exportStats.messageCount} messages • ~{exportStats.estimatedPages} pages
          </div>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={() => handleExport()}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            <div>
              <div className="font-medium">Full Export</div>
              <div className="text-xs text-gray-500">All messages and attachments</div>
            </div>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={() => handleExport({ includeAttachments: false })}
            className="flex items-center gap-2"
          >
            <FileText className="w-4 h-4" />
            <div>
              <div className="font-medium">Text Only</div>
              <div className="text-xs text-gray-500">Messages without attachments</div>
            </div>
          </DropdownMenuItem>
          
          {exportStats.messageCount > 50 && (
            <DropdownMenuItem 
              onClick={() => handleExport({ maxMessages: 50 })}
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              <div>
                <div className="font-medium">Recent 50</div>
                <div className="text-xs text-gray-500">Last 50 messages only</div>
              </div>
            </DropdownMenuItem>
          )}
          
          {exportStats.hasCodeBlocks && (
            <div className="px-2 py-1 text-xs text-blue-600 bg-blue-50 dark:bg-blue-900/20 rounded mx-2 my-1">
              💡 Code blocks will be formatted with syntax highlighting
            </div>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      
      {/* Error message tooltip */}
      <AnimatePresence>
        {exportState === 'error' && errorMessage && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 mt-2 p-2 bg-red-500 text-white text-xs rounded shadow-lg z-50 max-w-xs"
          >
            {errorMessage}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
