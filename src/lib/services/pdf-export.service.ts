/**
 * PDF Export Service
 * 
 * @description
 * Service for generating and downloading PDF exports of chat conversations.
 * Handles the conversion of message data to PDF format and manages the download process.
 */

import { pdf } from '@react-pdf/renderer';
import { saveAs } from 'file-saver';
import { Message } from '@/types';
import { ChatPDFDocument } from '@/components/chat/pdf-document';
import React from 'react';

export interface PDFExportOptions {
  filename?: string;
  includeMetadata?: boolean;
  includeAttachments?: boolean;
  maxMessages?: number;
}

export interface ConversationData {
  id: string;
  title: string;
  messages: Message[];
  createdAt?: Date;
  updatedAt?: Date;
  totalTokens?: number;
  model?: string;
}

export class PDFExportService {
  /**
   * Export a conversation to PDF and trigger download
   */
  static async exportConversationToPDF(
    conversation: ConversationData,
    options: PDFExportOptions = {}
  ): Promise<void> {
    const {
      filename,
      includeMetadata = true,
      includeAttachments = true,
      maxMessages
    } = options;

    try {
      // Prepare messages for export
      let messagesToExport = conversation.messages;
      
      // Limit messages if specified
      if (maxMessages && messagesToExport.length > maxMessages) {
        messagesToExport = messagesToExport.slice(-maxMessages);
      }

      // Filter out attachments if not included
      if (!includeAttachments) {
        messagesToExport = messagesToExport.map(msg => ({
          ...msg,
          attachments: undefined
        }));
      }

      // Calculate total tokens
      const totalTokens = messagesToExport.reduce((sum, msg) => {
        return sum + (msg.tokens?.total || 0);
      }, 0);

      // Create PDF document
      const pdfDocument = React.createElement(ChatPDFDocument, {
        messages: messagesToExport,
        conversationTitle: conversation.title,
        conversationId: conversation.id,
        exportDate: new Date(),
        totalMessages: messagesToExport.length,
        totalTokens: totalTokens > 0 ? totalTokens : undefined,
      });

      // Generate PDF blob
      const blob = await pdf(pdfDocument).toBlob();

      // Generate filename
      const exportFilename = filename || this.generateFilename(conversation.title);

      // Trigger download
      saveAs(blob, exportFilename);

      // Log successful export
      console.log('PDF export completed:', {
        conversationId: conversation.id,
        messageCount: messagesToExport.length,
        filename: exportFilename,
        sizeKB: Math.round(blob.size / 1024)
      });

    } catch (error) {
      console.error('PDF export failed:', error);
      throw new Error('Failed to generate PDF export. Please try again.');
    }
  }

  /**
   * Generate a safe filename for the PDF export
   */
  private static generateFilename(title: string): string {
    // Clean the title for use as filename
    const cleanTitle = title
      .replace(/[^a-zA-Z0-9\s-_]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .toLowerCase()
      .substring(0, 50); // Limit length

    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    
    return `chat-${cleanTitle}-${timestamp}.pdf`;
  }

  /**
   * Estimate PDF size before generation (rough calculation)
   */
  static estimatePDFSize(messages: Message[]): { estimatedSizeKB: number; warning?: string } {
    const avgCharsPerMessage = messages.reduce((sum, msg) => sum + msg.content.length, 0) / messages.length;
    const totalChars = messages.reduce((sum, msg) => sum + msg.content.length, 0);
    
    // Rough estimation: ~1KB per 1000 characters + overhead
    const estimatedSizeKB = Math.max(50, Math.round(totalChars / 800)); // Minimum 50KB
    
    let warning: string | undefined;
    
    if (messages.length > 100) {
      warning = 'Large conversation may take longer to generate';
    } else if (estimatedSizeKB > 5000) {
      warning = 'PDF may be very large (>5MB)';
    }

    return { estimatedSizeKB, warning };
  }

  /**
   * Validate conversation data before export
   */
  static validateConversationData(conversation: ConversationData): { valid: boolean; error?: string } {
    if (!conversation.id) {
      return { valid: false, error: 'Conversation ID is required' };
    }

    if (!conversation.title) {
      return { valid: false, error: 'Conversation title is required' };
    }

    if (!conversation.messages || conversation.messages.length === 0) {
      return { valid: false, error: 'No messages to export' };
    }

    // Check for very long messages that might cause issues
    const hasVeryLongMessage = conversation.messages.some(msg => msg.content.length > 50000);
    if (hasVeryLongMessage) {
      return { 
        valid: false, 
        error: 'Some messages are too long for PDF export. Please try exporting a smaller conversation.' 
      };
    }

    return { valid: true };
  }

  /**
   * Get export statistics for a conversation
   */
  static getExportStats(conversation: ConversationData): {
    messageCount: number;
    totalCharacters: number;
    estimatedPages: number;
    hasCodeBlocks: boolean;
    hasAttachments: boolean;
    dateRange: { start: Date; end: Date } | null;
  } {
    const messages = conversation.messages;
    
    const totalCharacters = messages.reduce((sum, msg) => sum + msg.content.length, 0);
    const hasCodeBlocks = messages.some(msg => msg.content.includes('```'));
    const hasAttachments = messages.some(msg => msg.attachments && msg.attachments.length > 0);
    
    // Rough page estimation (assuming ~2000 characters per page)
    const estimatedPages = Math.max(1, Math.ceil(totalCharacters / 2000));
    
    let dateRange: { start: Date; end: Date } | null = null;
    if (messages.length > 0) {
      const dates = messages.map(msg => new Date(msg.createdAt)).sort((a, b) => a.getTime() - b.getTime());
      dateRange = {
        start: dates[0],
        end: dates[dates.length - 1]
      };
    }

    return {
      messageCount: messages.length,
      totalCharacters,
      estimatedPages,
      hasCodeBlocks,
      hasAttachments,
      dateRange
    };
  }
}

// Export types for use in components
export { type PDFExportOptions, type ConversationData };
