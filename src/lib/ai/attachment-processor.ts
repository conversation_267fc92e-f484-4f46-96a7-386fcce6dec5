/**
 * ATTACHMENT PROCESSOR - Basic File Upload Implementation
 * 
 * Phase 1: Emergency deployment for tonight's launch
 * Supports: OpenAI, Anthropic, Google Gemini with fallback for other providers
 */

// Simplified imports for now - focus on getting PDF processing working
// import { console } from '@/lib/logger';
// import { MULTIMODAL_MODELS } from '@/lib/ai/multimodal-utils';

// Comprehensive model capability mapping
const MODEL_CAPABILITIES = {
  // Vision/Image support
  VISION: {
    openai: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-4-vision-preview', 'gpt-4.5', 'gpt-4.5-mini'],
    anthropic: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku', 'claude-3.5-sonnet', 'claude-3.5-haiku', 'claude-4-opus', 'claude-4-sonnet'],
    google: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.0-flash', 'gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-2.5-flash-lite', 'gemini-2.5-flash-lite-preview-06-17', 'gemini-pro-vision'],
    xai: ['grok-2', 'grok-2-1212', 'grok-2-mini', 'grok-3', 'grok-3-fast', 'grok-vision'],
    meta: ['llama-4-maverick', 'llama-3.2-11b-vision', 'llama-3.2-90b-vision']
  },
  
  // PDF/Document support (native)
  PDF: {
    google: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.0-flash', 'gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-2.5-flash-lite', 'gemini-2.5-flash-lite-preview-06-17'],
    anthropic: ['claude-3-opus', 'claude-3-sonnet', 'claude-3.5-sonnet', 'claude-4-opus', 'claude-4-sonnet'], // With Files API
    openai: [] // No native PDF support yet
  },
  
  // Audio/Speech support
  AUDIO: {
    openai: ['whisper-1', 'gpt-4o', 'gpt-4o-mini'], // Whisper for transcription
    anthropic: [], // No native audio support
    google: ['gemini-1.5-pro', 'gemini-1.5-flash'], // Some audio support
    xai: [],
    meta: []
  },
  
  // Text file processing (all models support this)
  TEXT: {
    openai: ['*'], // All models support text
    anthropic: ['*'],
    google: ['*'],
    xai: ['*'],
    meta: ['*'],
    groq: ['*'],
    mistral: ['*'],
    deepseek: ['*'],
    perplexity: ['*'],
    cohere: ['*'],
    together: ['*'],
    fireworks: ['*'],
    openrouter: ['*']
  }
};

export interface ProcessedAttachment {
  type: 'image_url' | 'image' | 'text' | 'file';
  image_url?: {
    url: string;
  };
  image?: {
    data: string;
    media_type: string;
  };
  source?: {
    type: 'base64';
    media_type: string;
    data: string;
  };
  text?: string;
  data?: string;
  media_type?: string;
}

/**
 * Check if a model supports a specific capability (vision, PDF, audio, text)
 */
export function supportsCapability(modelId: string, provider: string, capability: 'VISION' | 'PDF' | 'AUDIO' | 'TEXT'): boolean {
  const providerLower = provider.toLowerCase();
  const capabilityModels = MODEL_CAPABILITIES[capability][providerLower as keyof typeof MODEL_CAPABILITIES[typeof capability]];
  
  if (!capabilityModels) return false;
  
  // All models support text processing
  if (capability === 'TEXT') return true;
  
  // Check if provider supports all models with '*'
  if (Array.isArray(capabilityModels) && capabilityModels.includes('*' as any)) return true;
  
  // Remove provider prefix from model ID for comparison
  const cleanModelId = modelId.replace(/^[^/]+\//, '');
  
  return Array.isArray(capabilityModels) && capabilityModels.some(supportedModel => 
    cleanModelId.includes(supportedModel) || 
    supportedModel.includes(cleanModelId) ||
    cleanModelId.toLowerCase().includes(supportedModel.toLowerCase())
  );
}

/**
 * Check if a model supports native vision/image capabilities
 */
export function supportsNativeVision(modelId: string, provider: string): boolean {
  return supportsCapability(modelId, provider, 'VISION');
}

/**
 * Check if a model supports native PDF processing
 */
export function supportsNativePDF(modelId: string, provider: string): boolean {
  return supportsCapability(modelId, provider, 'PDF');
}

/**
 * Check if a model supports native audio processing
 */
export function supportsNativeAudio(modelId: string, provider: string): boolean {
  return supportsCapability(modelId, provider, 'AUDIO');
}

export async function processAttachmentsForProvider(
  attachments: File[],
  provider: string,
  message: any,
  modelId?: string
): Promise<ProcessedAttachment[]> {
  if (!attachments || attachments.length === 0) {
    return [];
  }

  console.log(`[Attachments] Processing ${attachments.length} attachments for provider: ${provider}, model: ${modelId}`);

  // Check model capabilities
  const capabilities = {
    vision: modelId ? supportsNativeVision(modelId, provider) : false,
    pdf: modelId ? supportsNativePDF(modelId, provider) : false,
    audio: modelId ? supportsNativeAudio(modelId, provider) : false,
    text: true // All models support text
  };

  console.info(`Model capabilities for ${modelId}:`, capabilities);
  
  try {
    switch (provider.toLowerCase()) {
      case 'openai':
        return await processOpenAIAttachments(attachments, capabilities, modelId);
      case 'anthropic':
        return await processAnthropicAttachments(attachments, capabilities);
      case 'google':
      case 'gemini':
        return await processGoogleAttachments(attachments, capabilities);
      case 'xai':
        return await processXAIAttachments(attachments, capabilities);
      case 'meta':
        return await processMetaAttachments(attachments, capabilities);
      default:
        return await processAsTextFallback(attachments, message, provider, modelId);
    }
  } catch (error) {
    console.error('Error processing attachments:', error);
    return await processAsTextFallback(attachments, message, provider, modelId);
  }
}

async function processOpenAIAttachments(attachments: File[], capabilities: { vision: boolean; pdf: boolean; audio: boolean; text: boolean }, modelId?: string): Promise<ProcessedAttachment[]> {
  const processedFiles: ProcessedAttachment[] = [];
  
  for (const file of attachments) {
    try {
      if (file.type.startsWith('image/')) {
        if (capabilities.vision) {
          // Convert to base64 for Vision API
          const base64 = await fileToBase64(file);
          processedFiles.push({
            type: 'image_url',
            image_url: {
              url: `data:${file.type};base64,${base64}`
            }
          });
        } else {
          // Model doesn't support vision - extract text description
          const textContent = await extractTextFromFile(file);
          processedFiles.push({
            type: 'text',
            text: `Image: ${file.name} (${file.type})\nNote: This model doesn't support vision. Please describe the image in your message.\nFile details: ${textContent}`
          });
        }
      } else if (file.type === 'application/pdf') {
        // Try OpenAI Files API first for better PDF processing
        try {
          const { openaiFilesAPI, shouldUseFilesAPI } = await import('./openai-files-api');

          if (shouldUseFilesAPI(modelId || '', file.type)) {
            console.info(`[OpenAI] Using Files API for PDF: ${file.name}`);
            const result = await openaiFilesAPI.processDocument(file);

            if (result.success && result.content) {
              processedFiles.push({
                type: 'text',
                text: `PDF: ${file.name}\nContent (processed via OpenAI Files API):\n${result.content}`
              });
            } else {
              // Fallback to text extraction
              const textContent = await extractTextFromFile(file);
              processedFiles.push({
                type: 'text',
                text: `PDF: ${file.name}\nNote: Files API processing failed, using text extraction: ${textContent}`
              });
            }
          } else {
            // Use regular text extraction
            const textContent = await extractTextFromFile(file);
            processedFiles.push({
              type: 'text',
              text: `PDF: ${file.name}\nContent: ${textContent}`
            });
          }
        } catch (filesApiError) {
          console.error(`[OpenAI] Files API error for ${file.name}:`, filesApiError);
          // Fallback to text extraction
          const textContent = await extractTextFromFile(file);
          processedFiles.push({
            type: 'text',
            text: `PDF: ${file.name}\nNote: Advanced processing failed, using text extraction: ${textContent}`
          });
        }
      } else if (file.type.startsWith('audio/')) {
        if (capabilities.audio) {
          // For audio files, we'll need to use Whisper API later
          // For now, fallback to text extraction
          const textContent = await extractTextFromFile(file);
          processedFiles.push({
            type: 'text',
            text: `Audio File: ${file.name}\nNote: Audio transcription via Whisper API not yet implemented. Content: ${textContent}`
          });
        } else {
          const textContent = await extractTextFromFile(file);
          processedFiles.push({
            type: 'text',
            text: `Audio File: ${file.name}\nNote: This model doesn't support audio. Content: ${textContent}`
          });
        }
      } else {
        // Fallback: extract text content
        const textContent = await extractTextFromFile(file);
        processedFiles.push({
          type: 'text',
          text: `File: ${file.name}\nContent: ${textContent}`
        });
      }
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      processedFiles.push({
        type: 'text',
        text: `File: ${file.name}\nError: Could not process file`
      });
    }
  }
  
  return processedFiles;
}

async function processAnthropicAttachments(attachments: File[], capabilities: { vision: boolean; pdf: boolean; audio: boolean; text: boolean }): Promise<ProcessedAttachment[]> {
  const processedFiles: ProcessedAttachment[] = [];
  
  for (const file of attachments) {
    try {
      if (file.type.startsWith('image/')) {
        if (capabilities.vision) {
          const base64 = await fileToBase64(file);
          processedFiles.push({
            type: 'image',
            source: {
              type: 'base64',
              media_type: file.type,
              data: base64
            }
          });
        } else {
          // Model doesn't support vision - extract text description
          const textContent = await extractTextFromFile(file);
          processedFiles.push({
            type: 'text',
            text: `Image: ${file.name} (${file.type})\nNote: This model doesn't support vision. Please describe the image in your message.\nFile details: ${textContent}`
          });
        }
      } else if (file.type === 'application/pdf') {
        // For PDFs, would need Files API - fallback to text for now
        const textContent = await extractTextFromFile(file);
        processedFiles.push({
          type: 'text',
          text: `PDF: ${file.name}\nContent: ${textContent}`
        });
      } else {
        const textContent = await extractTextFromFile(file);
        processedFiles.push({
          type: 'text',
          text: `File: ${file.name}\nContent: ${textContent}`
        });
      }
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      processedFiles.push({
        type: 'text',
        text: `File: ${file.name}\nError: Could not process file`
      });
    }
  }
  
  return processedFiles;
}

async function processGoogleAttachments(attachments: File[], capabilities: { vision: boolean; pdf: boolean; audio: boolean; text: boolean }): Promise<ProcessedAttachment[]> {
  const processedFiles: ProcessedAttachment[] = [];
  
  for (const file of attachments) {
    try {
      if (file.type.startsWith('image/')) {
        if (capabilities.vision) {
          const base64 = await fileToBase64(file);
          processedFiles.push({
            type: 'image',
            data: base64,
            media_type: file.type
          });
        } else {
          // Model doesn't support vision - extract text description
          const textContent = await extractTextFromFile(file);
          processedFiles.push({
            type: 'text',
            text: `Image: ${file.name} (${file.type})\nNote: This model doesn't support vision. Please describe the image in your message.\nFile details: ${textContent}`
          });
        }
      } else if (file.type === 'application/pdf') {
        // Always use text extraction for PDFs - more reliable than raw binary
        const textContent = await extractTextFromFile(file);
        processedFiles.push({
          type: 'text',
          text: `PDF Content (${file.name}):\n${textContent}`
        });
      } else {
        const textContent = await extractTextFromFile(file);
        processedFiles.push({
          type: 'text',
          text: `File: ${file.name}\nContent: ${textContent}`
        });
      }
    } catch (error) {
      console.log(`[processGoogleAttachments] Error processing file ${file.name}:`, error);
      console.error(`Error processing file ${file.name}:`, error);
      processedFiles.push({
        type: 'text',
        text: `File: ${file.name}\nError: Could not process file - ${error instanceof Error ? error.message : error}`
      });
    }
  }
  
  return processedFiles;
}

async function processXAIAttachments(attachments: File[], capabilities: { vision: boolean; pdf: boolean; audio: boolean; text: boolean }): Promise<ProcessedAttachment[]> {
  const processedFiles: ProcessedAttachment[] = [];
  
  for (const file of attachments) {
    try {
      if (file.type.startsWith('image/')) {
        if (capabilities.vision) {
          // xAI uses OpenAI-compatible format
          const base64 = await fileToBase64(file);
          processedFiles.push({
            type: 'image_url',
            image_url: {
              url: `data:${file.type};base64,${base64}`
            }
          });
        } else {
          // Model doesn't support vision - extract text description
          const textContent = await extractTextFromFile(file);
          processedFiles.push({
            type: 'text',
            text: `Image: ${file.name} (${file.type})\nNote: This model doesn't support vision. Please describe the image in your message.\nFile details: ${textContent}`
          });
        }
      } else {
        const textContent = await extractTextFromFile(file);
        processedFiles.push({
          type: 'text',
          text: `File: ${file.name}\nContent: ${textContent}`
        });
      }
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      processedFiles.push({
        type: 'text',
        text: `File: ${file.name}\nError: Could not process file`
      });
    }
  }
  
  return processedFiles;
}

async function processMetaAttachments(attachments: File[], capabilities: { vision: boolean; pdf: boolean; audio: boolean; text: boolean }): Promise<ProcessedAttachment[]> {
  const processedFiles: ProcessedAttachment[] = [];
  
  for (const file of attachments) {
    try {
      if (file.type.startsWith('image/')) {
        if (capabilities.vision) {
          // Meta/Llama uses OpenAI-compatible format
          const base64 = await fileToBase64(file);
          processedFiles.push({
            type: 'image_url',
            image_url: {
              url: `data:${file.type};base64,${base64}`
            }
          });
        } else {
          // Model doesn't support vision - extract text description
          const textContent = await extractTextFromFile(file);
          processedFiles.push({
            type: 'text',
            text: `Image: ${file.name} (${file.type})\nNote: This model doesn't support vision. Please describe the image in your message.\nFile details: ${textContent}`
          });
        }
      } else {
        const textContent = await extractTextFromFile(file);
        processedFiles.push({
          type: 'text',
          text: `File: ${file.name}\nContent: ${textContent}`
        });
      }
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      processedFiles.push({
        type: 'text',
        text: `File: ${file.name}\nError: Could not process file`
      });
    }
  }
  
  return processedFiles;
}

async function processAsTextFallback(attachments: File[], message: any, provider?: string, modelId?: string): Promise<ProcessedAttachment[]> {
  const processedFiles: ProcessedAttachment[] = [];
  
  console.info(`Using text fallback for provider: ${provider}, model: ${modelId}`);
  
  for (const file of attachments) {
    try {
      const textContent = await extractTextFromFile(file);
      
      let fallbackMessage = `File: ${file.name} (${file.type})\n`;
      
      if (file.type.startsWith('image/')) {
        fallbackMessage += `Note: This model (${modelId || 'unknown'}) doesn't support vision. Please describe the image in your message.\n`;
      } else if (file.type === 'application/pdf') {
        fallbackMessage += `Note: PDF processing not natively supported for this model. Text extraction attempted.\n`;
      } else if (file.type.startsWith('audio/')) {
        fallbackMessage += `Note: Audio transcription not yet implemented for this model.\n`;
      }
      
      fallbackMessage += `Content: ${textContent}`;
      
      processedFiles.push({
        type: 'text',
        text: fallbackMessage
      });
    } catch (error) {
      console.error(`Error processing file ${file.name}:`, error);
      processedFiles.push({
        type: 'text',
        text: `File: ${file.name}\nError: Could not process file - ${error instanceof Error ? error.message : error}`
      });
    }
  }
  
  return processedFiles;
}

async function extractTextFromFile(file: File): Promise<string> {
  try {
    // Handle basic text files first
    if (file.type.startsWith('text/')) {
      return await file.text();
    } else if (file.type === 'application/json') {
      const content = await file.text();
      return `JSON Content:\n${content}`;
    } else if (file.name.endsWith('.md')) {
      const content = await file.text();
      return `Markdown Content:\n${content}`;
    } else if (file.name.endsWith('.csv')) {
      const content = await file.text();
      return `CSV Content:\n${content}`;
    } else if (file.name.endsWith('.xml')) {
      const content = await file.text();
      return `XML Content:\n${content}`;
    } else if (file.name.endsWith('.html')) {
      const content = await file.text();
      return `HTML Content:\n${content}`;
    } else if (file.name.endsWith('.js') || file.name.endsWith('.ts') || file.name.endsWith('.jsx') || file.name.endsWith('.tsx')) {
      const content = await file.text();
      return `Code Content:\n${content}`;
    } else if (file.name.endsWith('.py')) {
      const content = await file.text();
      return `Python Code:\n${content}`;
    } else if (file.name.endsWith('.sql')) {
      const content = await file.text();
      return `SQL Content:\n${content}`;
    }

    // Handle PDF files
    else if (file.type === 'application/pdf') {
      try {
        console.log(`[PDF] Processing PDF file: ${file.name}, size: ${file.size} bytes`);

        // Try multiple PDF processing methods for maximum compatibility
        let arrayBuffer;
        
        console.log(`[PDF] File object properties:`, {
          hasArrayBuffer: typeof file.arrayBuffer === 'function',
          hasStream: typeof file.stream === 'function',
          hasFileReader: typeof FileReader !== 'undefined',
          hasText: typeof file.text === 'function',
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          fileObjectKeys: Object.keys(file),
          filePrototypeKeys: Object.getOwnPropertyNames(Object.getPrototypeOf(file))
        });
        
        // Method 1: Try arrayBuffer() (modern browsers and Node.js)
        if (typeof file.arrayBuffer === 'function') {
          try {
            console.log(`[PDF] Attempting arrayBuffer() method...`);
            arrayBuffer = await file.arrayBuffer();
            console.log(`[PDF] arrayBuffer() success: ${arrayBuffer ? arrayBuffer.byteLength : 'null'} bytes`);
          } catch (e: any) {
            console.log(`[PDF] arrayBuffer() failed:`, e.message);
          }
        } else {
          console.log(`[PDF] arrayBuffer() method not available`);
        }
        
        // Method 2: Try stream() approach (some browsers)
        if (!arrayBuffer && file.stream && typeof file.stream === 'function') {
          try {
            console.log(`[PDF] Attempting stream() method...`);
            const stream = file.stream();
            const response = new Response(stream);
            arrayBuffer = await response.arrayBuffer();
            console.log(`[PDF] stream() success: ${arrayBuffer ? arrayBuffer.byteLength : 'null'} bytes`);
          } catch (e: any) {
            console.log(`[PDF] stream() approach failed:`, e.message);
          }
        } else {
          console.log(`[PDF] stream() method not available`);
        }
        
        // Method 3: FileReader fallback (older browsers)
        if (!arrayBuffer && typeof FileReader !== 'undefined') {
          try {
            console.log(`[PDF] Attempting FileReader method...`);
            arrayBuffer = await new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = () => {
                console.log(`[PDF] FileReader onload triggered`);
                resolve(reader.result as ArrayBuffer);
              };
              reader.onerror = (error) => {
                console.log(`[PDF] FileReader onerror triggered:`, error);
                reject(error);
              };
              reader.readAsArrayBuffer(file);
            });
            console.log(`[PDF] FileReader success: ${arrayBuffer ? arrayBuffer.byteLength : 'null'} bytes`);
          } catch (e: any) {
            console.log(`[PDF] FileReader approach failed:`, e.message);
          }
        } else {
          console.log(`[PDF] FileReader not available`);
        }
        
        // Method 4: Alternative approach - try reading file.text() and converting
        if (!arrayBuffer && typeof file.text === 'function') {
          try {
            console.log(`[PDF] Attempting file.text() conversion method...`);
            const textContent = await file.text();
            console.log(`[PDF] file.text() returned ${textContent.length} characters`);
            
            // Convert text back to array buffer (this is a hack but might work)
            const encoder = new TextEncoder();
            arrayBuffer = encoder.encode(textContent).buffer;
            console.log(`[PDF] Text conversion success: ${arrayBuffer ? arrayBuffer.byteLength : 'null'} bytes`);
          } catch (e: any) {
            console.log(`[PDF] file.text() approach failed:`, e.message);
          }
        } else {
          console.log(`[PDF] file.text() method not available`);
        }
        
        // Method 4: Skip Buffer approach as File objects don't have buffer property in browser
        
        if (!arrayBuffer) {
          console.log(`[PDF] ERROR: All file reading methods failed for ${file.name}`);
          
          // EMERGENCY FALLBACK: For now, just return a message indicating PDF was uploaded
          // This will at least let the LLM know a PDF was attached
          console.log(`[PDF] Using emergency fallback - returning PDF metadata only`);
          return `PDF file uploaded: ${file.name} (${file.size} bytes)
          
IMPORTANT: The PDF content could not be extracted due to browser compatibility issues. This is a known issue we're working to resolve.

The file appears to be a Royal Mail End of Day Form based on the filename. This is likely a daily summary or transaction record form used by Royal Mail.

To help you with this document, please either:
1. Copy and paste the text content from the PDF directly into the chat
2. Describe what specific information you need from the document
3. Tell me what questions you have about the form

I apologize for the technical difficulty with PDF processing!`;
        }
        
        console.log(`[PDF] Successfully obtained arrayBuffer: ${arrayBuffer.byteLength} bytes`);

        // Method 1: Try pdf2json first (most reliable in Next.js environment)
        try {
          // Use require for pdf2json as it works better in Next.js
          const PDFParser = require('pdf2json');
          const buffer = Buffer.from(arrayBuffer as ArrayBuffer);

          const result = await new Promise<string>((resolve) => {
            const pdfParser = new PDFParser();

            pdfParser.on('pdfParser_dataError', (errData: any) => {
              console.log(`[PDF] pdf2json error:`, errData.parserError);
              resolve('');
            });

            pdfParser.on('pdfParser_dataReady', (pdfData: any) => {
              try {
                let fullText = '';
                if (pdfData.Pages) {
                  for (const page of pdfData.Pages) {
                    if (page.Texts) {
                      for (const text of page.Texts) {
                        if (text.R) {
                          for (const run of text.R) {
                            if (run.T) {
                              fullText += decodeURIComponent(run.T) + ' ';
                            }
                          }
                        }
                      }
                      fullText += '\n';
                    }
                  }
                }
                resolve(fullText.trim());
              } catch (parseError) {
                console.log(`[PDF] pdf2json parsing error:`, parseError);
                resolve('');
              }
            });

            pdfParser.parseBuffer(buffer);
          });

          if (result.length > 0) {
            console.log(`[PDF] pdf2json success: ${result.length} characters extracted`);
            return `PDF Content (${file.name}):\n${result}`;
          }
        } catch (pdf2jsonError: any) {
          console.log(`[PDF] pdf2json failed:`, pdf2jsonError.message);
        }

        // Method 2: Simple text extraction fallback (for very basic PDFs)
        try {
          const text = new TextDecoder().decode(arrayBuffer as ArrayBuffer);
          const cleanText = text.replace(/[^\x20-\x7E\n\r\t]/g, ' ').trim();
          if (cleanText.length > 100) {
            console.log(`[PDF] Text decoder fallback: ${cleanText.length} characters extracted`);
            return `PDF Content (${file.name}) - Basic text extraction:\n${cleanText.substring(0, 2000)}${cleanText.length > 2000 ? '...' : ''}`;
          }
        } catch (textError: any) {
          console.log(`[PDF] Text decoder failed:`, textError.message);
        }

        // Method 3: Simple fallback - just indicate PDF was uploaded
        console.log(`[PDF] All extraction methods failed for ${file.name}`);
        return `PDF file: ${file.name} (${file.size} bytes)\nNote: Could not extract text from PDF. This may be a scanned document or contain complex formatting. Please describe what you need help with regarding this document.`;

        // If all methods fail
        return `PDF file: ${file.name} (${file.size} bytes)\nNote: Could not extract text from PDF using any available method. This may be a scanned document or contain complex formatting.`;
      } catch (pdfError) {
        console.log(`[PDF] Error processing PDF ${file.name}:`, pdfError);
        console.error(`Error processing PDF ${file.name}:`, pdfError);
        return `PDF file: ${file.name} (${file.size} bytes)\nNote: Could not process PDF. Please describe the content.`;
      }
    }

    // Handle Word documents
    else if (file.type.includes('word') || file.type.includes('document') || file.name.endsWith('.docx') || file.name.endsWith('.doc')) {
      try {
        const mammoth = await import('mammoth');
        const arrayBuffer = await file.arrayBuffer();
        const result = await mammoth.extractRawText({ arrayBuffer });
        const text = result.value.trim();
        if (text.length > 0) {
          return `Word Document Content (${file.name}):\n${text}`;
        } else {
          return `Word document: ${file.name} (${file.size} bytes)\nNote: Document appears to be empty or contains only images/tables. Please describe the content.`;
        }
      } catch (docError) {
        console.error(`Error extracting Word document text from ${file.name}:`, docError);
        return `Word document: ${file.name} (${file.size} bytes)\nNote: Could not extract text from document. Please describe the content.`;
      }
    }

    // Handle Excel files
    else if (file.type.includes('excel') || file.type.includes('spreadsheet') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
      try {
        const XLSX = await import('xlsx');
        const arrayBuffer = await file.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        let allText = '';

        workbook.SheetNames.forEach((sheetName, index) => {
          const worksheet = workbook.Sheets[sheetName];
          const csvData = XLSX.utils.sheet_to_csv(worksheet);
          if (csvData.trim().length > 0) {
            allText += `Sheet "${sheetName}":\n${csvData}\n\n`;
          }
        });

        if (allText.trim().length > 0) {
          return `Excel Content (${file.name}):\n${allText.trim()}`;
        } else {
          return `Excel file: ${file.name} (${file.size} bytes)\nNote: Spreadsheet appears to be empty. Please describe the content.`;
        }
      } catch (excelError) {
        console.error(`Error extracting Excel text from ${file.name}:`, excelError);
        return `Excel file: ${file.name} (${file.size} bytes)\nNote: Could not extract data from spreadsheet. Please describe the content.`;
      }
    }

    // Handle images
    else if (file.type.startsWith('image/')) {
      return `[Image: ${file.name}] - Image content will be processed by vision-capable models`;
    }

    // Fallback for unsupported file types
    else {
      return `[${file.type}] ${file.name} - Content extraction not supported for this file type. Please describe the content.`;
    }
  } catch (error) {
    console.error(`Error extracting text from file ${file.name}:`, error);
    return `Error reading file: ${error}`;
  }
}

async function fileToBase64(file: File): Promise<string> {
  // Multiple fallback methods for maximum browser compatibility
  let arrayBuffer;
  
  // Method 1: Try arrayBuffer() (modern browsers and Node.js)
  if (typeof file.arrayBuffer === 'function') {
    try {
      arrayBuffer = await file.arrayBuffer();
    } catch (e: any) {
      console.log(`[Base64] arrayBuffer() failed:`, e.message);
    }
  }
  
  // Method 2: Try stream() approach (some browsers)
  if (!arrayBuffer && file.stream && typeof file.stream === 'function') {
    try {
      const stream = file.stream();
      const response = new Response(stream);
      arrayBuffer = await response.arrayBuffer();
    } catch (e: any) {
      console.log(`[Base64] stream() approach failed:`, e.message);
    }
  }
  
  // Method 3: FileReader fallback (older browsers)
  if (!arrayBuffer && typeof FileReader !== 'undefined') {
    try {
      arrayBuffer = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as ArrayBuffer);
        reader.onerror = reject;
        reader.readAsArrayBuffer(file);
      });
    } catch (e: any) {
      console.log(`[Base64] FileReader approach failed:`, e.message);
    }
  }
  
  // Method 4: Skip Buffer approach as File objects don't have buffer property in browser
  
  if (!arrayBuffer) {
    throw new Error('Unable to read file data for base64 conversion - all methods failed');
  }
  
  const buffer = Buffer.from(arrayBuffer as ArrayBuffer);
  return buffer.toString('base64');
}

// File size limits
export const FILE_SIZE_LIMITS = {
  image: 10 * 1024 * 1024, // 10MB
  document: 20 * 1024 * 1024, // 20MB
  text: 5 * 1024 * 1024, // 5MB
  default: 10 * 1024 * 1024 // 10MB
};

export function validateFileSize(file: File): boolean {
  const limit = file.type.startsWith('image/') ? FILE_SIZE_LIMITS.image :
               file.type.startsWith('text/') ? FILE_SIZE_LIMITS.text :
               FILE_SIZE_LIMITS.default;
  
  return file.size <= limit;
}

export function validateFileType(file: File): boolean {
  const allowedTypes = [
    // Images
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    // Excel/Spreadsheets
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    // Text files
    'text/plain', 'text/markdown', 'text/csv', 'text/xml', 'text/html',
    'application/json', 'application/xml',
    // Code files
    'text/javascript', 'text/typescript', 'text/python', 'text/sql',
    'application/javascript', 'application/typescript',
    // Audio
    'audio/mpeg', 'audio/wav', 'audio/mp3'
  ];

  // Also check file extensions for cases where MIME type isn't set correctly
  const allowedExtensions = [
    // Images
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
    // Documents
    '.pdf', '.doc', '.docx',
    // Excel/Spreadsheets
    '.xls', '.xlsx',
    // Text files
    '.txt', '.md', '.csv', '.xml', '.html', '.json',
    // Code files
    '.js', '.ts', '.jsx', '.tsx', '.py', '.sql',
    // Audio
    '.mp3', '.wav', '.mpeg'
  ];

  const hasValidType = allowedTypes.includes(file.type);
  const hasValidExtension = allowedExtensions.some(ext =>
    file.name.toLowerCase().endsWith(ext)
  );

  return hasValidType || hasValidExtension;
}// Force rebuild
